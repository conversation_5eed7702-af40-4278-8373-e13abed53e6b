import React, { useState } from 'react';
import { animated, useTransition, to } from '@react-spring/web';
import { Group } from '@visx/group';
import { ParentSize } from '@visx/responsive';
import { scaleOrdinal } from '@visx/scale';
import Pie, {
  type ProvidedProps,
  type PieArcDatum,
} from '@visx/shape/lib/shapes/Pie';
import { format } from 'numerable';
import { Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

interface DataPoint {
  label: string;
  usage: number;
  color: string;
}

// accessor functions
const usage = (d: DataPoint) => d.usage;

const defaultMargin = { top: 10, right: 10, bottom: 10, left: 10 };

export type PieProps = {
  width: number;
  height: number;
  margin?: typeof defaultMargin;
  animate?: boolean;
  circulatingSupply: number;
  totalSupply: number;
  data: DataPoint[];
};

export function Chart({
  width,
  height,
  margin = defaultMargin,
  animate = true,
  data,
  totalSupply,
  circulatingSupply,
}: PieProps) {
  const [selectedData, setSelectedData] = useState<DataPoint | undefined>(
    data[2]
  );

  if (width < 10) return null;

  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;
  const radius = Math.min(innerWidth, innerHeight) / 2;
  const centerY = innerHeight / 2;
  const centerX = innerWidth / 2;
  const donutThickness = 30;

  // color scales
  const getBrowserColor = scaleOrdinal({
    domain: data.map(({ label }) => label),
    range: [
      'rgb(109, 109, 109)',
      'rgb(199, 255, 127)',
      'rgb(0, 219, 188)',
      '#EB5347',
    ],
  });

  return (
    <div className='relative flex items-center justify-center'>
      {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
      <svg width={width} height={height}>
        <rect
          rx={14}
          width={width}
          height={height}
          fill="url('#visx-pie-gradient')"
        />
        <Group top={centerY + margin.top} left={centerX + margin.left}>
          <Pie
            data={data}
            pieValue={usage}
            outerRadius={radius}
            innerRadius={radius - donutThickness}
            cornerRadius={3}
            padAngle={0.005}
          >
            {(pie) => (
              <AnimatedPie<DataPoint>
                {...pie}
                animate={animate}
                getKey={(arc) => arc.data.label}
                onClickDatum={() => {}}
                getColor={(arc) => getBrowserColor(arc.data.label)}
                onMouseEnter={(arc) => {
                  setSelectedData(
                    data.find((item) => item.label === arc.data.label)
                  );
                }}
              />
            )}
          </Pie>
        </Group>
      </svg>
      {selectedData ? (
        <div className='absolute flex flex-col items-center'>
          <Text className={selectedData.color}>{selectedData.label}</Text>
          <Text
            level='xl'
            className={cn('flex items-center gap-1', selectedData.color)}
          >
            {format(
              (selectedData.usage /
                (selectedData.label === 'Unissued'
                  ? totalSupply
                  : circulatingSupply)) *
                100,
              '0.00'
            )}
            %{' '}
            <span className='font-light text-[#707070]'>
              of{' '}
              {selectedData.label === 'Unissued'
                ? format(totalSupply, '0a')
                : format(circulatingSupply, '0.00a')}
            </span>
          </Text>
        </div>
      ) : null}
    </div>
  );
}

// react-spring transition definitions
type AnimatedStyles = { startAngle: number; endAngle: number; opacity: number };

const fromLeaveTransition = ({ endAngle }: PieArcDatum<any>) => ({
  // enter from 360° if end angle is > 180°
  startAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
  endAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
  opacity: 0,
});
const enterUpdateTransition = ({ startAngle, endAngle }: PieArcDatum<any>) => ({
  startAngle,
  endAngle,
  opacity: 1,
});

type AnimatedPieProps<Datum> = ProvidedProps<Datum> & {
  animate?: boolean;
  getKey: (d: PieArcDatum<Datum>) => string;
  getColor: (d: PieArcDatum<Datum>) => string;
  onClickDatum: (d: PieArcDatum<Datum>) => void;
  delay?: number;
  onMouseEnter: (d: PieArcDatum<Datum>) => void;
};

function AnimatedPie<Datum>({
  animate,
  arcs,
  path,
  getKey,
  getColor,
  onClickDatum,
  onMouseEnter,
}: AnimatedPieProps<Datum>) {
  const transitions = useTransition<PieArcDatum<Datum>, AnimatedStyles>(arcs, {
    from: animate ? fromLeaveTransition : enterUpdateTransition,
    enter: enterUpdateTransition,
    update: enterUpdateTransition,
    leave: animate ? fromLeaveTransition : enterUpdateTransition,
    keys: getKey,
  });
  return transitions((props, arc, { key }) => {
    return (
      <g
        key={key}
        onMouseEnter={() => {
          onMouseEnter(arc);
        }}
      >
        <animated.path
          // compute interpolated path d attribute from intermediate angle values
          d={to([props.startAngle, props.endAngle], (startAngle, endAngle) =>
            path({
              ...arc,
              startAngle,
              endAngle,
            })
          )}
          fill={getColor(arc)}
          onClick={() => {
            onClickDatum(arc);
          }}
          onTouchStart={() => {
            onClickDatum(arc);
          }}
        />
      </g>
    );
  });
}

export const PieChart = (props: PieProps) => {
  return (
    <ParentSize>
      {({ width, height }) => (
        <Chart {...props} width={width} height={height} />
      )}
    </ParentSize>
  );
};
